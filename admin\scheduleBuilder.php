<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsRotation.php');
include('../setRequest.php');
include('../class/clsHospitalSite.php');
include('../class/clsStudent.php');


$BASEPATH = BASE_PATH;

$objRotation = new clsRotation();
$parentRotations = $objRotation->GetParentRotations($currentSchoolId);
unset($objRotation);

//CREATE OBJECT
$objStudent = new clsStudent();
$rowsSchoolStudents = $objStudent->GetAllSchoolStudents($currentSchoolId, 0, 0);
$totalSchoolStudents = 0;

if ($rowsSchoolStudents != '') {
    $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
}
$studentListArray = array();

if ($totalSchoolStudents > 0) {
    while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
        $studentId = $row['studentId'];
        $firstName = stripslashes($row['firstName']);
        $lastName = stripslashes($row['lastName']);
        $rank = stripslashes($row['rank']);

        $fullName = $firstName . ' ' . $lastName;
        $studentListArray[] = array(
            "id" => $studentId,
            "name" => $fullName,
            "rank" => $rank
        );
    }
}


//Get Hospitals
//----------------------------------------------------------------------------------------
$objHospitalSite = new clsHospitalSite();
$hospitalsiteList = $objHospitalSite->GetAllHospitalSiteForSchedule($currentSchoolId);
$totalHospitalsite = ($hospitalsiteList != '') ? mysqli_num_rows($hospitalsiteList) : 0;
$hospitalSiteArray = array();
if ($hospitalsiteList != "") {
    while ($rowHospital = mysqli_fetch_assoc($hospitalsiteList)) {
        $hositalId  = $rowHospital['hospitalSiteId'];
        $hospitalName  = stripslashes($rowHospital['title']);
        $clockIn  = $rowHospital['clockIn'];
        $clockOut  = $rowHospital['clockOut'];
        $hospitalDays  = $rowHospital['hospitalDays'];
        $dailyVisits  = $rowHospital['dailyVisits'];
        $hospitalSiteCode  = $rowHospital['hospitalSiteCode'];
        $hospitalDaysTitle  = $rowHospital['hospitalDaysTitle'];
        $hospitalDaysTitle = explode(',', $hospitalDaysTitle);
        $time = ($clockIn != '' && $clockOut != '') ? $clockIn . '-' . $clockOut : '';
        // add Attribute to select for clockIn & clockOut
        $hospitalSiteArray[] = array(
            "id" => $hositalId,
            "name" => $hospitalName,
            "clockIn" => $clockIn,
            "clockOut" => $clockOut,
            "time" => $time,
            "hospitalDays" => $hospitalDays,
            "days" => $hospitalDaysTitle,
            "capacity" => $dailyVisits,
            "hospitalSiteCode" => $hospitalSiteCode
        );
    }
}
$hospitalSiteJson = json_encode($hospitalSiteArray);
$studentListJson = json_encode($studentListArray);
unset($objHospitalSite);

// echo '<pre>';
// print_r($hospitalSiteArray);
// // print_r($studentListArray);
// echo '</pre>';
// exit;
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clinical Schedule Builder</title>
    <?php include('includes/headercss.php'); ?>
    <!-- Add jQuery and FullCalendar libraries -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <link href="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.css" rel="stylesheet">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/scheduleBuilder.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/stepper.css">

    <style>
        .form-group {
            position: relative;
            margin-bottom: 20px;
        }

        .form-group .calendar-icon {
            position: absolute;
            right: 10px;
            top: 46px;
            transform: translateY(-50%);
            cursor: pointer;
            color: #555;
        }

        .form-group input[type="date"] {
            padding-right: 35px;
            /* space for icon */
        }

        .label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .input {
            width: 100%;
            padding: 8px 10px;
            box-sizing: border-box;
        }

        /* Fix for drag positioning */
        /* .fc-event-dragging {
    pointer-events: none !important;
    transform: none !important;
    will-change: transform;
} */

        /* Remove FullCalendar's highlight element */
        /* .fc-highlight {
    display: none !important;
}

/* Better dragging appearance */
        .fc-event-dragging {
            opacity: 0.8;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 9999 !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f9f9 !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f9f9 !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            text-wrap: wrap;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered {
            box-sizing: border-box;
            list-style: none;
            margin: 0;
            padding: 5px 10px;
            width: 100%;
        }

        .studdent-multiselect>.select2-container--default {
            border-left: 3px solid red !important;
            border-radius: 12px !important;
        }

        */
    </style>

    <!-- <style>
        /* Event card styling */
        .fc-daygrid-event {
            margin-bottom: 1px !important;
            padding: 3px !important;
            border-radius: 4px;
        }
        
        /* Delete button styling */
        .fc-event-delete {
            font-size: 16px;
            font-weight: bold;
            color: #fff;
            background: rgba(0,0,0,0.3);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .fc-event-delete:hover {
            background: rgba(0,0,0,0.5);
        }
        
        /* Fix event card gap */
        .fc-daygrid-day-frame {
            padding: 1px !important;
        }
        
        /* Event card hover effect */
        .fc-event {
            transition: all 0.2s ease;
        }
        .fc-event:hover {
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
            transform: translateY(-1px);
        }
    </style> -->

    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .fc-event-dragging {
            transform: none !important;
        }

        .cards-grid {
            display: grid;
            /* grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); */
            gap: 20px;
        }

        .site-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px;
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            height: 100% !important;
        }

        .site-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-color: #3b82f6;
        }

        .site-card.selected {
            border-color: #3b82f6;
            background: linear-gradient(135deg, #dbeafe 0%, #f0f9ff 100%);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
        }

        .site-info {
            display: flex;
            align-items: flex-start;
            /* Align checkbox with the top of text */
            gap: 10px;
            flex-wrap: nowrap;
        }

        .site-name {
            font-size: 13px;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4px;
            white-space: normal;
            /* Allow wrapping */
            word-break: break-word;
            /* Ensure long words wrap */
            flex: 1;
            /* Allow text to take full remaining space */
            max-width: calc(100% - 24px);
            /* Prevent overflow (24px ~ checkbox width + gap) */
        }


        .site-id {
            font-size: 12px;
            color: #64748b;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            position: relative;
            background: white;
            cursor: pointer;
            margin-left: auto;
            /* Push the checkbox to the right */

        }

        .checkbox.checked {
            background: #3b82f6;
            border-color: #3b82f6;
        }

        .checkbox.checked::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        .details-grid {
            display: grid;
            /* grid-template-columns: 1fr 1fr; */
            gap: 12px;
        }


        .detail-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .detail-icon {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .icon-time {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
        }

        .icon-code {
            background: rgba(99, 102, 241, 0.1);
            color: #6366f1;
        }

        .icon-capacity {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .icon-days {
            background: rgba(245, 158, 11, 0.1);
            color: #f59e0b;
        }

        .detail-content {
            flex: 1;
            min-width: 0;
        }

        .detail-label {
            font-size: 10px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #64748b;
            margin-bottom: 2px;
        }

        .detail-value {
            font-size: 13px;
            font-weight: 500;
            color: #1e293b;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .days-full {
            grid-column: 1 / -1;
        }

        .days-full .detail-value {
            white-space: normal;
            font-size: 12px;
        }

        .selection-indicator {
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            border: 2px solid #3b82f6;
            border-radius: 12px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .selected .selection-indicator {
            opacity: 1;
        }

        .pulse-dot {
            position: absolute;
            top: 8px;
            left: 8px;
            width: 8px;
            height: 8px;
            background: #3b82f6;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {

            0%,
            100% {
                opacity: 1;
            }

            50% {
                opacity: 0.5;
            }
        }

        /* Icons using CSS */
        .icon::before {
            font-size: 14px;
        }

        .icon-clock::before {
            content: '🕐';
        }

        .icon-hash::before {
            content: '#';
        }

        .icon-users::before {
            content: '👥';
        }

        .icon-calendar::before {
            content: '📅';
        }

        .icon-map::before {
            content: '📍';
        }

        /* Fix for site selection cards */
        .selection-card1 {
            position: relative;
            /* Add this */
            background-color: #fff;
            border-radius: 0.5rem;
            cursor: pointer;
            margin-bottom: 0;
            padding: 0;
            /* Remove padding if it exists */
        }

        /* Make sure the checkbox is properly positioned and clickable */
        .selection-card1 .checkbox {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            /* Hide visually but keep clickable */
            z-index: 1;
            /* Make sure it's above other elements */
            cursor: pointer;
        }

        /* Style the custom checkbox */
        .selection-card1 .custom-checkbox {
            position: relative;
            z-index: 0;
            /* Keep below the real checkbox */
            width: 1.25rem;
            height: 1.25rem;
            border: 2px solid #d1d5db;
            border-radius: 0.25rem;
            margin-right: 0.75rem;
            transition: all 0.2s ease;
        }

        /* Selected state for site cards */
        .selection-card1.selected {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }

        .selection-card1.selected .custom-checkbox {
            background-color: #5CB85C;
            border-color: #5CB85C;
        }

        .selection-card1.selected .custom-checkbox:after {
            content: '';
            position: absolute;
            left: 4px;
            top: 0px;
            width: 6px;
            height: 12px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        /* .site-info {
    display: flex;
    align-items: center;
    gap: 10px;
} */

        .site-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            gap: 10px;
        }
    </style>

    <style>
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            border-left-color: #09f;
            animation: spin 1s linear infinite;
            margin: auto;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }


        /* Calendar Container */
        #calendar {
            max-width: 1200px;
            margin: 0 auto;
            font-family: Arial, sans-serif;
        }

        /* Event Styling */
        .fc-event {
            border: none;
            border-radius: 6px;
            padding: 3px 6px;
            margin: 1px;
            font-size: 0.8em;
            cursor: move;
            position: relative;
            overflow: visible; /* Changed to visible for remove button */
            text-overflow: ellipsis;
            white-space: nowrap;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        /* Event Hover Effect */
        .fc-event:hover {
            opacity: 0.95;
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
            transform: translateY(-1px);
            z-index: 100;
        }

        /* Ensure event content doesn't interfere with remove button */
        .fc-event-main {
            position: relative;
            z-index: 1;
        }

        /* Modern Remove Button */
        .remove-event-btn {
            display: none;
            position: absolute;
            top: -6px;
            right: -6px;
            background: #dc3545;
            color: white;
            border: 2px solid white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 11px;
            font-weight: bold;
            line-height: 16px;
            text-align: center;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
            z-index: 1000;
        }

        .remove-event-btn:hover {
            background: #c82333;
            transform: scale(1.1);
            box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
        }

        /* Show Remove Button on Event Hover */
        .fc-event:hover .remove-event-btn {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Alternative: Icon-based remove button */
        .remove-event-btn-icon {
            display: none;
            position: absolute;
            top: -8px;
            right: -8px;
            background: #dc3545;
            color: white;
            border: 2px solid white;
            border-radius: 50%;
            width: 22px;
            height: 22px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
            transition: all 0.2s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .remove-event-btn-icon:hover {
            background: #c82333;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .remove-event-btn-icon::before {
            content: "✕";
            font-size: 12px;
            font-weight: bold;
        }

        /* Show Icon Remove Button on Event Hover */
        .fc-event:hover .remove-event-btn-icon {
            display: flex;
        }

        /* Additional styling for better visual hierarchy */
        .fc-event {
            border-left: 3px solid rgba(255, 255, 255, 0.3) !important;
        }

        .fc-event:hover {
            border-left: 3px solid rgba(255, 255, 255, 0.6) !important;
        }

        /* Smooth transitions for all event interactions */
        .fc-event, .remove-event-btn {
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Prevent remove button from affecting event layout */
        .fc-event {
            padding-right: 8px; /* Extra space for remove button */
        }

        /* Custom tooltip for remove button */
        .remove-event-btn::after {
            content: attr(title);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s;
            z-index: 1001;
        }

        .remove-event-btn:hover::after {
            opacity: 1;
        }

        /* Full Event Name on Hover */
        .fc-event:hover::after {
            content: attr(title);
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px;
            border-radius: 4px;
            z-index: 1000;
            white-space: normal;
            max-width: 300px;
            left: 100%;
            top: 0;
        }

        /* Custom CSS for dragging events */
        .fc-event-dragging {
            opacity: 0.75;
            z-index: 9999 !important;
            transform: none !important;
            pointer-events: none !important;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
            border-radius: 6px !important;
        }

        /* Ensure the event follows the cursor closely */
        .fc-daygrid-event-harness {
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Improve drag ghost positioning */
        .fc-event-dragging .fc-event-main {
            pointer-events: none !important;
        }

        /* Better cursor alignment during drag */
        .fc-event {
            will-change: transform;
        }

        .fc-event:hover {
            cursor: grab;
        }

        .fc-event:active {
            cursor: grabbing;
        }

        /* Additional styling for dragging events */
        .dragging-event {
            transform: none !important;
            transition: none !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.4) !important;
            border: 2px solid rgba(59, 130, 246, 0.8) !important;
        }

        /* Prevent text selection during drag */
        .fc-event-dragging * {
            user-select: none !important;
            -webkit-user-select: none !important;
            -moz-user-select: none !important;
            -ms-user-select: none !important;
        }

        /* Visual feedback for card selection */
        .selection-card,
        .selection-card1 {
            transition: all 0.2s ease;
        }

        .selection-card:active,
        .selection-card1:active {
            transform: scale(0.98);
        }

        .selection-card.selecting,
        .selection-card1.selecting {
            opacity: 0.7;
            transform: scale(0.99);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            #calendar {
                max-width: 100%;
                padding: 10px;
            }

            .fc-event {
                font-size: 0.7em;
            }
        }

        .select2-selection__rendered {
            border-left: solid 3px red !important;
            border-radius: 12px !important;

        }

        .form-stepper-horizontal {
            position: relative;
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: justify;
            -ms-flex-pack: justify;
            justify-content: space-between;
            padding: 0 30px;
            width: 80% !important;
        }

    
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="schedule-container">
        <!-- Header -->
        <!-- <div class="header">
            <h1>Clinical Schedule Builder</h1>
        </div> -->

        <!-- stepper start -->
        <div id="multi-step-form-container">
            <!-- Form Steps / Progress Bar -->
            <ul class="form-stepper form-stepper-horizontal text-center mx-auto">
                <!-- Step 1 -->
                <li class="form-stepper-active text-center form-stepper-list" step="1">
                    <a class="mx-2">
                        <span class="form-stepper-circle">
                            <span>1</span>
                        </span>
                        <div class="label stepper-label">Basic Info</div>
                    </a>
                </li>
                <!-- Step 2 -->
                <li class="form-stepper-unfinished text-center form-stepper-list" step="2">
                    <a class="mx-2">
                        <span class="form-stepper-circle text-muted">
                            <span>2</span>
                        </span>
                        <div class="label text-muted stepper-label">Students</div>
                    </a>
                </li>
                <!-- Step 3 -->
                <li class="form-stepper-unfinished text-center form-stepper-list" step="3">
                    <a class="mx-2">
                        <span class="form-stepper-circle text-muted">
                            <span>3</span>
                        </span>
                        <div class="label text-muted stepper-label">Hospital Sites</div>
                    </a>
                </li>
                <!-- Step 4 -->
                <li class="form-stepper-unfinished text-center form-stepper-list" step="4">
                    <a class="mx-2">
                        <span class="form-stepper-circle text-muted">
                            <span>4</span>
                        </span>
                        <div class="label text-muted stepper-label">Preferences & Generate</div>
                    </a>
                </li>
            </ul>
        </div>
        <!-- stepper end -->

        <!-- Step 1: Basic Info -->
        <div class="step-content active" id="step-1">
            <div class="card">
                <div class="card-header" style="display: none;">
                    <h2 class="card-title">Schedule Information</h2>
                    <p class="card-description">Set up the basic details for your clinical schedule</p>
                </div>

                <div style="position: relative; z-index: 1;" >
                    <div class="grid grid-2" style="gap: 2rem;">

                        <div class="form-group">
                            <label class="label" for="schedule-title">Schedule Title</label>
                            <input type="text" id="schedule-title" style="cursor:text;" class="input required-input" value="test" placeholder="Enter schedule title">
                        </div>
                        <div class="form-group">
                            <label class="label" for="cborotation">Rotation</label>
                            <select id="cborotation" class="form-control input-md required-input select2_single">
                                <option value="">Select Rotation</option>
                                <?php
                                while ($row = mysqli_fetch_assoc($parentRotations)) {
                                    $selrotationId  = $row['rotationId'];
                                    $name  = stripslashes($row['title']);

                                ?>
                                    <option value="<?php echo ($selrotationId); ?>" <?php if ($selrotationId == '15136') { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                }
                                ?>
                            </select>
                        </div>
                    </div>
                    <div class="grid grid-2" style="gap: 2rem;">
                        <div class="form-group">
                            <label class="label" for="start-date">Start Date</label>
                            <div class='input-group date' id='start-date-group' style="width: 100%;">
                                <input type='text' name="start-date" id="start-date" class="form-control input input-md required-input" placeholder="MM/DD/YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="label" for="end-date">End Date</label>
                            <div class='input-group date' id='end-date-group' style="width: 100%;">
                                <input type='text' name="end-date" id="end-date" class="form-control input input-md required-input" placeholder="MM/DD/YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="schedule-preferences">
                    <h3 class="section-title">Schedule Preferences</h3>
                    <div class="grid grid-2" style="gap: 2rem;">


                        <div class="form-group">
                            <label class="label" for="holiday-dates">Holiday Dates</label>

                            <div class="input-group date" id="holiday-dates-group" style="width: 100%;">
                                <input
                                    type="text"
                                    id="holiday-dates"
                                    class="form-control input input-md"
                                    placeholder="Click calendar to select multiple dates..." />
                                <span class="input-group-addon calender-icon" style="cursor: pointer;">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="label" style="margin-top: 33px;">
                                <input type="checkbox" id="include-weekends" style="margin-right: 0.5rem;">
                                Include Saturday/Sunday Schedules
                            </label>
                        </div>
                    </div>
                </div>

                <div class="navigation">
                    <div></div>
                    <button class=" button" onclick="nextStep()">Continue to Students</button>
                </div>
            </div>
        </div>

        <!-- Step 2: Students -->
        <div class="step-content" id="step-2">
            <div class="card">
                <div class="card-header" style="display: none;">
                    <h2 class="card-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                            <circle cx="9" cy="7" r="4" />
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                            <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                        </svg>
                        Select Students
                    </h2>
                    <p class="card-description">Choose the students who will participate in the clinical rotations</p>
                </div>

                <div class="search-actions">
                    <div class="search-container">
                        <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input type="text" id="student-search" class="input search-input" style=" height: 37px;" placeholder="Search students...">
                    </div>
                    <div class="actions">
                        <label class="select-all-label" style=" display: flex;gap: 10px;">
                            <input type="checkbox" id="select-all-checkbox" class="checkbox" style="width: 15px;">
                            <span style="margin-top: 3px; ">Select All</span>
                        </label>
                    </div>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <span class="badge badge-secondary" id="student-count">0 of <?php echo $totalSchoolStudents; ?> students selected</span>
                </div>
                <div class="grid-scroll-wrapper" style="height: 285px; overflow-y:auto !important">

                    <div class="grid grid-3" id="students-grid"></div>
                </div>
                <div class="navigation">
                    <button class="btn btn-outline" onclick="prevStep()" style="cursor: pointer;
    padding: 10px 30px;
    border-radius: 10px;    margin-right: 15px !important;
    border: 1px solid #5cb85c;
    background-color: #ffffff;
    color: #5cb85c;">Back</button>
                    <button class=" button" id="students-continue" onclick="nextStep() " disabled>Continue to Sites (0)</button>
                </div>
            </div>
        </div>

        <!-- Step 3: Hospital Sites -->
        <div class="step-content" id="step-3">
            <div class="card">
                <div class="card-header " style="display: none;">
                    <h2 class="card-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                            <circle cx="12" cy="10" r="3" />
                        </svg>
                        Select Hospital Sites
                    </h2>
                    <p class="card-description">Choose the clinical rotation locations for your students</p>
                </div>

                <!-- Add the sites per student field at the top -->
                <div class="form-group" style="margin-bottom: 0.5rem;  border-radius: 0.5rem;">
                    <label class="label" for="sites-per-student">
                        How many unique hospital sites should each student visit?
                        <input type="number" id="sites-per-student" class="input" value="3" min="1" style="width: 80px; margin-left: 0.5rem; display: inline-block;">
                    </label>
                    <small style="display: none; color: #6b7280; margin-top: 0.5rem;">This determines how many different sites each student will rotate through during the entire schedule period.</small>
                </div>
                <hr style=" margin-top: 5px; margin-bottom: 14px;">

                <div class="search-actions">
                    <div class="search-container">
                        <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input type="text" id="site-search" class="input search-input" placeholder="Search sites or types...">
                    </div>
                    <div class="actions">
                        <label class="select-all-label" style=" display: flex;gap: 10px;">
                            <input type="checkbox" id="select-all-sites-checkbox" class="checkbox" style="width: 15px;">
                            <span style="margin-top: 3px; ">Select All</span>
                        </label>
                    </div>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <span class="badge badge-secondary" id="site-count">0 of <?php echo $totalHospitalsite; ?> sites selected</span>
                </div>

                <div class="grid grid-2" id="sites-grid"></div>
                <!-- <div class="site-rules-section">
                    <h3 class="section-title">Hospital Site Rules</h3>
                    <div class="site-rules-container grid grid-3" id="site-rules-container">
                    </div>
                </div> -->
                <div class="navigation">
                    <button class="btn btn-outline" onclick="prevStep()" style="cursor: pointer;
    padding: 10px 30px;
    border-radius: 10px;    margin-right: 15px !important;
    border: 1px solid #5cb85c;
    background-color: #ffffff;
    color: #5cb85c;">Back</button>
                    <button class="button" id="sites-continue" onclick="nextStep()" disabled>Continue to Preferences (0)</button>
                </div>
            </div>
        </div>

        <!-- Step 4: Combined Preferences & Generate -->
        <div class="step-content" id="step-4">
            <div class="card">
                <div class="card-header" style="display: none;">
                    <h2 class="card-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3" />
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
                        </svg>
                        Schedule Preferences & Generate
                    </h2>
                    <p class="card-description">Configure scheduling rules and generate your clinical schedule</p>
                </div>

                <!-- Preferences Section -->

                <button style="cursor: pointer;
    padding: 10px 30px;
    border-radius: 10px;
    margin-right: 15px !important;
    border: 1px solid #5cb85c;
    background-color: #ffffff;
    color: #5cb85c;" class=" btn-outline" onclick="prevStep()">Back</button>
                <!-- Summary Section -->
             <div class="grid grid-3" style="margin: 2rem 0; grid-template-columns: repeat(3, 1fr);">
                    <div style="text-align: center; padding: 1rem; background: #eff6ff; border-radius: 0.5rem;">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#3b82f6" stroke-width="2" style="margin: 0 auto 0.5rem;">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                            <circle cx="9" cy="7" r="4" />
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                            <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                        </svg>
                        <p style="font-size: 1.5rem; font-weight: 700; color: #3b82f6;" id="summary-students">0</p>
                        <p style="font-size: 0.875rem; color: #6b7280;">Students Selected</p>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #ecfdf5; border-radius: 0.5rem;">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#5CB85C" stroke-width="2" style="margin: 0 auto 0.5rem;">
                            <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z" />
                            <circle cx="12" cy="10" r="3" />
                        </svg>
                        <p style="font-size: 1.5rem; font-weight: 700; color: #5CB85C;" id="summary-sites">0</p>
                        <p style="font-size: 0.875rem; color: #6b7280;">Hospital Sites</p>
                    </div>
                    <div style="text-align: center; padding: 1rem; background: #f3e8ff; border-radius: 0.5rem;">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#7c3aed" stroke-width="2" style="margin: 0 auto 0.5rem;">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
                            <line x1="16" y1="2" x2="16" y2="6" />
                            <line x1="8" y1="2" x2="8" y2="6" />
                            <line x1="3" y1="10" x2="21" y2="10" />
                        </svg>
                        <p style="font-size: 1.5rem; font-weight: 700; color: #7c3aed;" id="summary-days">0</p>
                        <p style="font-size: 0.875rem; color: #6b7280;">Days Duration</p>
                    </div>
                </div>


                <button id="export-json-btn" class="btn btn-secondary" onclick="exportScheduleAsJson()">
                    Export JSON
                </button>
                <button id="export-student-json-btn" class="btn btn-secondary" onclick="exportStudentSpecificJson()">
                    Export Student JSON
                </button>
                <!-- Action Buttons -->

            </div>

            <!-- Calendar Container -->
            <div id="calendar-container" style="display: none;">
                <div class="card">
                    <!-- <div class="card-header">
                        <h3 class="card-title">Generated Schedule</h3>
                    </div> -->
                    <div style="display: flex; justify-content:space-between;">
                        <div class="search-container" style="margin-bottom:17px;">
                            <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                            <input type="text" id="unified-search" class="input search-input" placeholder="Search students or hospitals...">

                        </div>
                        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 1.5rem;">


                            <button class="button" onclick="exportSchedule()" id="export-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7,10 12,15 17,10" />
                                    <line x1="12" y1="15" x2="12" y2="3" />
                                </svg>
                                Export CSV
                            </button>
                            <button class="button" onclick="exportSchedule()" id="export-btn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                                    <polyline points="7,10 12,15 17,10" />
                                    <line x1="12" y1="15" x2="12" y2="3" />
                                </svg>
                                Save </button>

                        </div>
                    </div>
                    <div id="calendar"></div>
                </div>
            </div>
        </div>

        <!-- Calendar View (New Section) -->
        <div class="step-content" id="calendar-view">
            <div class="card">
                <div class="card-header">
                    <h2 class="card-title">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        Schedule Calendar
                    </h2>
                    <p class="card-description">View and filter the generated clinical schedule</p>
                </div>
                <div id="search-loader" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 1000;">
                    <div class="spinner"></div>
                </div>

                <!-- Filters Section -->
                <div class="filters-section">
                    <div class="grid grid-2" style="gap: 2rem;">
                        <!-- Student Filter -->
                        <div class="form-group">
                            <label class="label" for="calendar-student-filter">Filter by Student</label>
                            <div class="search-container" style="max-width: 100%;">
                                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input type="text" id="calendar-student-filter" class="input search-input" placeholder="Search students...">
                            </div>
                        </div>

                        <!-- Hospital Site Filter -->
                        <div class="form-group">
                            <label class="label" for="calendar-site-filter">Filter by Hospital Site</label>
                            <div class="search-container" style="max-width: 100%;">
                                <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                </svg>
                                <input type="text" id="calendar-site-filter" class="input search-input" placeholder="Search hospital sites...">
                            </div>
                        </div>
                    </div>

                    <!-- Filter Actions -->
                    <div class="filter-actions" style="display: flex; justify-content: flex-end; margin-top: 1rem;">
                        <button class="btn btn-outline" onclick="resetCalendarFilters()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 12a9 9 0 1 0 18 0 9 9 0 0 0-18 0z" />
                                <path d="M9 12l2 2 4-4" />
                            </svg>
                            Reset Filters
                        </button>
                    </div>
                </div>
                <div class="search-container">
                    <svg class="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <input type="text" id="unified-search" class="input search-input" placeholder="Search students or hospitals...">
                </div>
                <!-- Calendar Container -->
                <div id="calendar-container">
                    <div id="calendar"></div>
                </div>

                <!-- Calendar Legend -->
                <div class="calendar-legend" id="calendar-legend">
                    <!-- Legend items will be dynamically added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading" id="loading">
        <div class="spinner"></div>
    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@6.1.8/index.global.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <style>
        .highlighted-holiday {
            background-color: #a8e6cf !important;
            /* A pleasant light green */
            border-radius: 4px;
        }
    </style>

    <script>
        $(window).load(function() {
            // $("#cborotation").select2();

        });
        // Data
        const STUDENTS = <?php echo json_encode($studentListArray); ?>;
        var hospitalSiteJson = '<?php echo $hospitalSiteJson; ?>';
        const HOSPITAL_SITES = JSON.parse(hospitalSiteJson);
        console.log(HOSPITAL_SITES);
        // const HOSPITAL_SITES = {
        //     "Bon Temps Medical Center": {
        //         time: "8am–12pm",
        //         days: ["Tuesday", "Wednesday", "Thursday", "Friday"],
        //         capacity: 3,
        //     },
        //     "Demo Community College": {
        //         time: "12pm–4pm",
        //         days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        //         capacity: 4,
        //     },
        //     "Desert of the Real Hospital": {
        //         time: "10am–2pm",
        //         days: ["Monday", "Wednesday", "Friday", "Saturday"],
        //         capacity: 2,
        //     },
        //     "Enterprise Medical Center ": {
        //         time: "11am–3pm",
        //         days: ["Tuesday", "Thursday", "Friday", "Sunday"],
        //         capacity: 3,
        //         type: "Urgent Care"
        //     },
        //     "Hospital Site 1": {
        //         time: "9am–1pm",
        //         days: ["Monday", "Tuesday", "Wednesday", "Thursday"],
        //         capacity: 5,
        //         type: "Hospital"
        //     },
        //     "Hospital Site 127127": {
        //         time: "1pm–5pm",
        //         days: ["Wednesday", "Thursday", "Friday", "Saturday"],
        //         capacity: 2,
        //         type: "Clinic"
        //     },
        //     "Northwood Pediatrics": {
        //         time: "8am–12pm",
        //         days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
        //         capacity: 3,
        //         type: "Pediatrics"
        //     },
        //     "Southside Wellness Center": {
        //         time: "10am–2pm",
        //         days: ["Tuesday", "Wednesday", "Thursday", "Friday"],
        //         capacity: 4,
        //         type: "Wellness"
        //     }
        // };

        // State
        let currentStep = 1;
        let selectedStudents = [];
        let selectedSites = [];
        let scheduleEvents = [];

        // Debouncing variables
        let siteSelectionTimeout = null;
        let studentSelectionTimeout = null;

        // Debouncing utility function
        function debounce(func, wait, immediate) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    timeout = null;
                    if (!immediate) func(...args);
                };
                const callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func(...args);
            };
        }

        // Track last selection times to prevent rapid successive clicks
        let lastSiteSelectionTime = {};
        let lastStudentSelectionTime = {};

        // Debug logging function
        function debugLog(message, data = null) {
            if (console && console.log) {
                const timestamp = new Date().toLocaleTimeString();
                console.log(`[${timestamp}] ${message}`, data || '');
            }
        }

        // Initialize
        $(document).ready(function() {
            console.log("DOM Content Loaded");

            $('#start-date-group').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#end-date-group').datetimepicker({
                format: 'MM/DD/YYYY',
                useCurrent: false
            });
            $("#start-date-group").on("dp.change", function(e) {
                console.log("Start date selected:", e.date);
                $('#end-date-group').data("DateTimePicker").minDate(e.date);
            });

            $("#end-date-group").on("dp.change", function(e) {
                console.log("End date selected:", e.date);
                $('#start-date-group').data("DateTimePicker").maxDate(e.date);
            });


            // Initialize the calendar immediately
            initializeCalendar();

            // Other initializations
            initializeStudents();
            initializeSites();
            updateSummary();

            // Search functionality
            $('#student-search').on('input', filterStudents);
            $('#site-search').on('input', filterSites);

            // Add unified search handler
            $('#unified-search').on('input', debounce(filterCalendarEvents, 300));

            // Add event listeners for select-all checkboxes
            $('#select-all-checkbox').on('change', selectAllStudents);
            $('#select-all-sites-checkbox').on('change', selectAllSites);

            // Add stepper click handler
            $('.form-stepper-list').click(function() {
                const stepId = parseInt($(this).attr('step'));
                const currentActiveStep = parseInt($('.form-stepper-active').attr('step'));

                // Allow navigation to previous steps or current step
                if (stepId <= currentActiveStep) {
                    navigateToFormStep(stepId);
                }
            });
        });

        function initializeStudents() {
            const $grid = $('#students-grid');
            $grid.empty();

            $.each(STUDENTS, function(i, student) {
                const $card = $('<div>', {
                    class: 'selection-card',
                    'data-student-id': student.id
                }).html(`
                    <div class="selection-card-content">
                        <div class="custom-checkbox"></div>
                        <div class="selection-card-text">
                            <div class="selection-card-title">${student.name}</div>
                            <div class="selection-card-details">
                                <div>Rank: ${student.rank}</div>
                            </div>
                        </div>
                    </div>
                    <input type="checkbox" class="checkbox" value="${student.id}" style="display:none">
                `);

                $card.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Check if this student was recently clicked (within 300ms)
                    const now = Date.now();
                    if (lastStudentSelectionTime[student.id] && (now - lastStudentSelectionTime[student.id]) < 300) {
                        debugLog(`Student ${student.id} clicked too rapidly, ignoring`);
                        return;
                    }
                    lastStudentSelectionTime[student.id] = now;

                    // Clear any existing timeout
                    if (studentSelectionTimeout) {
                        clearTimeout(studentSelectionTimeout);
                    }

                    // Add visual feedback
                    $card.addClass('selecting');

                    // Debounce the selection to prevent rapid clicks
                    studentSelectionTimeout = setTimeout(function() {
                        $card.removeClass('selecting');
                        toggleStudentSelection(student);
                    }, 150);
                });

                // Prevent child elements from triggering parent click
                $card.find('.custom-checkbox, .selection-card-title, .selection-card-details').on('click', function(e) {
                    e.stopPropagation();
                    // Trigger parent click manually to ensure consistent behavior
                    $card.trigger('click');
                });

                $grid.append($card);
            });
        }

        function initializeSites() {
            const $grid = $('#sites-grid');
            $grid.empty();

            // Clear selected sites array when reinitializing
            selectedSites = [];

            // Clear existing site rules
            const $rulesContainer = $('#site-rules-container');
            $rulesContainer.empty();

            $.each(HOSPITAL_SITES, function(i, siteInfo) {
                const siteName = siteInfo.name;
                const siteId = siteInfo.id;
                const siteCode = siteInfo.hospitalSiteCode;
                const siteCapacity = (siteInfo.capacity > 0 ? siteInfo.capacity : 3);
                const siteTime = siteInfo.time || '9:00 AM - 5:00 PM';



                // Set default days (Monday to Friday) if days array is empty
                let displayDays = siteInfo.days;
                if (!displayDays || !displayDays.length || (displayDays.length === 1 && displayDays[0] === "")) {
                    displayDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
                    // Also update the original object for later use
                    siteInfo.days = displayDays;
                }

                const $card = $('<div>', {
                    class: 'selection-card1',
                    'data-site': siteId
                }).html(`
    <div class="site-card">
                <div class="card-header">
                    <div class="site-info">
                        <div class="custom-checkbox"></div>
                        <div class="site-name">${siteName}</div>
                    </div>
                </div>

                <div class="details-grid">
                    <div class="detail-item">
                        <div class="detail-icon icon-time">
                            <span class="icon icon-clock"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Time Of Day</div>
                            <div class="detail-value">${siteTime || 'Not specified'}</div>
                        </div>
                    </div>

                    <div class="detail-item">
                        <div class="detail-icon icon-capacity">
                            <span class="icon icon-users"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Hospital Capacity</div>
                            <div class="detail-value">${siteCapacity || '3'} students</div>
                        </div>
                    </div>

                    <div class="detail-item days-full">
                        <div class="detail-icon icon-days">
                            <span class="icon icon-calendar"></span>
                        </div>
                        <div class="detail-content">
                            <div class="detail-label">Days Of The Week</div>
                            <div class="detail-value">${displayDays.join(', ')}</div>
                        </div>
                    </div>
                </div>

                <div class="selection-indicator"></div>
                <input type="checkbox" class="checkbox" value="${siteId}" style="display:none;">
            </div>
                `);

                $card.on('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Check if this site was recently clicked (within 300ms)
                    const now = Date.now();
                    if (lastSiteSelectionTime[siteId] && (now - lastSiteSelectionTime[siteId]) < 300) {
                        debugLog(`Site ${siteId} clicked too rapidly, ignoring`);
                        return;
                    }
                    lastSiteSelectionTime[siteId] = now;

                    // Clear any existing timeout
                    if (siteSelectionTimeout) {
                        clearTimeout(siteSelectionTimeout);
                    }

                    // Add visual feedback
                    $card.addClass('selecting');

                    // Debounce the selection to prevent rapid clicks
                    siteSelectionTimeout = setTimeout(function() {
                        $card.removeClass('selecting');
                        toggleSiteSelection(siteId);
                    }, 150);
                });

                // Prevent child elements from triggering parent click
                $card.find('.custom-checkbox, .detail-icon, .detail-content, .site-name').on('click', function(e) {
                    e.stopPropagation();
                    // Trigger parent click manually to ensure consistent behavior
                    $card.trigger('click');
                });

                $grid.append($card);

                // Create site rule card
                const $ruleDiv = $('<div>', {
                    class: 'site-rule',
                    'data-site': siteId,
                    style: 'display: none;' // Hide initially
                }).html(`
                    <div style="display: flex;align-items: center;gap: 1rem;justify-content: space-between; width:100%">
                        <h4 style="margin: 0;">${siteName}</h4>
                        <div class="form-group" style="margin: 0;width: 70px;">
                            <input type="number" class="max-students input" data-site="${siteId}"
                                   value="${siteCapacity || 3}" min="1" max="10" style="width: 70px;">
                        </div>
                    </div>
                `);
                $rulesContainer.append($ruleDiv);
            });

            // Update site count after initialization
            updateSiteCount();
        }

        function toggleStudentSelection(student) {
            const studentId = student.id;
            const index = selectedStudents.findIndex(s => s.id === studentId);

            if (index === -1) {
                selectedStudents.push(student);
                $(`.selection-card[data-student-id="${studentId}"]`).addClass('selected');
            } else {
                selectedStudents.splice(index, 1);
                $(`.selection-card[data-student-id="${studentId}"]`).removeClass('selected');
            }

            updateStudentCount();
            updateSummary();
        }

        function toggleSiteSelection(siteId) {
            // Convert siteId to string to ensure consistent comparison
            const siteIdStr = String(siteId);
            const index = selectedSites.findIndex(id => String(id) === siteIdStr);

            if (index === -1) {
                // Add site if not already selected
                selectedSites.push(siteIdStr);
                $(`.selection-card1[data-site="${siteId}"]`).addClass('selected');
                $(`.selection-card1[data-site="${siteId}"] .checkbox`).prop('checked', true);
                $(`.site-rule[data-site="${siteId}"]`).show();
                debugLog(`Site ${siteId} added. Total selected: ${selectedSites.length}`);
            } else {
                // Remove site if already selected
                selectedSites.splice(index, 1);
                $(`.selection-card1[data-site="${siteId}"]`).removeClass('selected');
                $(`.selection-card1[data-site="${siteId}"] .checkbox`).prop('checked', false);
                $(`.site-rule[data-site="${siteId}"]`).hide();
                debugLog(`Site ${siteId} removed. Total selected: ${selectedSites.length}`);
            }
            updateSiteCount();
            updateSummary();
        }

        function selectAllStudents() {
            const isChecked = $('#select-all-checkbox').prop('checked');

            debugLog(`Select All Students clicked: ${isChecked ? 'SELECT' : 'DESELECT'}`);
            debugLog('Current selected students before:', selectedStudents.map(s => s.id));

            if (isChecked) {
                // When selecting all, clear the array first and rebuild it
                selectedStudents = [];

                $.each(STUDENTS, function(i, student) {
                    const studentId = student.id;
                    const $card = $(`.selection-card[data-student-id="${studentId}"]`);

                    // Add to array (no need to check for duplicates since we cleared it)
                    selectedStudents.push(student);
                    $card.addClass('selected');
                });
            } else {
                // When deselecting all, clear everything
                selectedStudents = [];

                $.each(STUDENTS, function(i, student) {
                    const studentId = student.id;
                    const $card = $(`.selection-card[data-student-id="${studentId}"]`);

                    $card.removeClass('selected');
                });
            }

            debugLog('Selected students after:', selectedStudents.map(s => s.id));
            updateStudentCount();
            updateSummary();
        }

        function selectAllSites() {
            const isChecked = $('#select-all-sites-checkbox').prop('checked');

            debugLog(`Select All Sites clicked: ${isChecked ? 'SELECT' : 'DESELECT'}`);
            debugLog('Current selected sites before:', selectedSites);

            if (isChecked) {
                // When selecting all, clear the array first and rebuild it
                selectedSites = [];

                $('#sites-grid .selection-card1').each(function() {
                    const siteId = String($(this).data('site')); // Convert to string for consistency
                    const $checkbox = $(this).find('.checkbox');

                    // Add to array (no need to check for duplicates since we cleared it)
                    selectedSites.push(siteId);
                    $checkbox.prop('checked', true);
                    $(this).addClass('selected');
                    $(`.site-rule[data-site="${siteId}"]`).show();
                });
            } else {
                // When deselecting all, clear everything
                selectedSites = [];

                $('#sites-grid .selection-card1').each(function() {
                    const siteId = $(this).data('site');
                    const $checkbox = $(this).find('.checkbox');

                    $checkbox.prop('checked', false);
                    $(this).removeClass('selected');
                    $(`.site-rule[data-site="${siteId}"]`).hide();
                });
            }

            debugLog('Selected sites after:', selectedSites);
            updateSiteCount();
            updateSummary();
        }

        // Add event listeners using jQuery
        $(document).ready(function() {
            // Add the missing event handler for select-all-checkbox
            $('#select-all-checkbox').on('change', selectAllStudents);

            // Existing event handlers
            $('#select-all-sites-checkbox').on('change', selectAllSites);
            $('.select-all-label span').on('click', function(event) {
                event.preventDefault(); // Prevent the checkbox from being toggled
                const $checkbox = $(this).prev();
                $checkbox.prop('checked', !$checkbox.prop('checked')); // Toggle the checkbox state
                if ($checkbox.attr('id') === 'select-all-sites-checkbox') {
                    selectAllSites();
                } else if ($checkbox.attr('id') === 'select-all-checkbox') {
                    selectAllStudents();
                }
            });
        });

        function filterStudents() {
            const searchTerm = $('#student-search').val().toLowerCase();
            console.log("Filtering students with term:", searchTerm);

            $('#students-grid .selection-card').each(function() {
                const studentId = $(this).data('student-id');
                const student = STUDENTS.find(s => s.id == studentId);

                if (student) {
                    const studentName = student.name.toLowerCase();
                    const studentRank = (student.rank || '').toString().toLowerCase();

                    if (studentName.includes(searchTerm) || studentRank.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });
        }

        function filterSites() {
            const searchTerm = $('#site-search').val().toLowerCase();
            console.log("Filtering sites with term:", searchTerm);

            $('#sites-grid .selection-card1').each(function() {
                const siteId = $(this).data('site');
                const site = HOSPITAL_SITES.find(s => s.id == siteId);

                if (site) {
                    const siteName = site.name.toLowerCase();
                    const siteCode = (site.hospitalSiteCode || '').toString().toLowerCase();

                    if (siteName.includes(searchTerm) || siteCode.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                }
            });
        }

        function updateStudentCount() {
            document.getElementById('student-count').textContent = `${selectedStudents.length} of ${STUDENTS.length} students selected`;
            const continueBtn = document.getElementById('students-continue');
            if (selectedStudents.length > 0) {
                continueBtn.disabled = false;
                continueBtn.textContent = `Continue to Sites (${selectedStudents.length})`;
            } else {
                continueBtn.disabled = true;
                continueBtn.textContent = 'Continue to Sites (0)';
            }
        }

        function updateSiteCount() {
            document.getElementById('site-count').textContent = `${selectedSites.length} of ${Object.keys(HOSPITAL_SITES).length} sites selected`;
            const continueBtn = document.getElementById('sites-continue');
            if (selectedSites.length > 0) {
                continueBtn.disabled = false;
                continueBtn.textContent = `Continue to Generate (${selectedSites.length})`;
            } else {
                continueBtn.disabled = true;
                continueBtn.textContent = 'Continue to Generate (0)';
            }

            // Update max sites per student
            const sitesPerStudentInput = document.getElementById('sites-per-student');
            sitesPerStudentInput.max = selectedSites.length;
            if (parseInt(sitesPerStudentInput.value) > selectedSites.length) {
                sitesPerStudentInput.value = selectedSites.length;
            }

            updateSummary();
        }

        function updateSummary() {
            document.getElementById('summary-students').textContent = selectedStudents.length;
            document.getElementById('summary-sites').textContent = selectedSites.length;

            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                const days = Math.ceil((end - start) / (1000 * 60 * 60 * 24)) + 1;
                document.getElementById('summary-days').textContent = days;
            } else {
                document.getElementById('summary-days').textContent = '0';
            }
        }

        function nextStep() {
            if (currentStep === 1) {
                const title = document.getElementById('schedule-title').value.trim();
                const startDate = document.getElementById('start-date').value;
                const endDate = document.getElementById('end-date').value;

                // Uncomment if you want to enforce these validations
                // if (!title || !startDate || !endDate) {
                //     alert('Please fill in all required fields');
                //     return;
                // }
            }

            if (currentStep === 2 && selectedStudents.length === 0) {
                alert('Please select at least one student');
                return;
            }

            if (currentStep === 3 && selectedSites.length === 0) {
                alert('Please select at least one hospital site');
                return;
            }

            if (currentStep === 3) {
                // Show loading spinner
                document.getElementById('loading').style.display = 'flex';

                // Call generateSchedule when continuing from step 3 to step 4
                setTimeout(() => {
                    // Get selected students and sites directly from DOM
                    const studentsToSchedule = Array.from(document.querySelectorAll('.selection-card.selected[data-student-id]'))
                        .map(card => card.dataset.studentId);
                    const sitesToUse = Array.from(document.querySelectorAll('.selection-card1.selected[data-site]'))
                        .map(card => card.dataset.site);

                    // Call the new function with explicit parameters
                    generateMockScheduleWithParams(studentsToSchedule, sitesToUse);

                    // Hide loading spinner after generation is complete
                    document.getElementById('loading').style.display = 'none';

                    // Show calendar container
                    document.getElementById('calendar-container').style.display = 'block';

                    // Move to next step
                    currentStep++;
                    showStep(currentStep);

                    // Fix calendar display after a short delay
                    setTimeout(fixCalendarDisplay, 200);
                }, 10);
                return;
            }

            if (currentStep < 4) {
                currentStep++;
                showStep(currentStep);
            }
        }

        function prevStep() {
            if (currentStep > 1) {
                currentStep--;
                showStep(currentStep);
            }
        }

        function showStep(step) {
            // Hide all steps
            document.querySelectorAll('.step-content').forEach(content => {
                content.classList.remove('active');
            });

            // Show current step
            document.getElementById(`step-${step}`).classList.add('active');

            // Update stepper progress indicators
            updateStepperProgress(step);
            updateSummary();
        }

        function updateStepperProgress(step) {
            // Remove all active and completed classes
            document.querySelectorAll('.form-stepper-list').forEach(stepEl => {
                stepEl.classList.remove('form-stepper-active', 'form-stepper-completed');
                stepEl.classList.add('form-stepper-unfinished');

                const circle = stepEl.querySelector('.form-stepper-circle');
                const label = stepEl.querySelector('.label');

                circle.classList.add('text-muted');
                label.classList.add('text-muted');
            });

            // Update current and completed steps
            document.querySelectorAll('.form-stepper-list').forEach((stepEl, index) => {
                const stepNumber = parseInt(stepEl.getAttribute('step'));
                const circle = stepEl.querySelector('.form-stepper-circle');
                const label = stepEl.querySelector('.label');

                if (stepNumber < step) {
                    // Completed steps
                    stepEl.classList.remove('form-stepper-unfinished');
                    stepEl.classList.add('form-stepper-completed');
                    circle.classList.remove('text-muted');
                    label.classList.remove('text-muted');
                } else if (stepNumber === step) {
                    // Active step
                    stepEl.classList.remove('form-stepper-unfinished');
                    stepEl.classList.add('form-stepper-active');
                    circle.classList.remove('text-muted');
                    label.classList.remove('text-muted');
                }
            });
        }

        function navigateToFormStep(stepNumber) {
            currentStep = stepNumber;
            showStep(stepNumber);
        }

        function generateSchedule() {
            console.log("Generate Schedule button clicked");

            // Get selected students and sites directly from DOM
            const studentsToSchedule = Array.from(document.querySelectorAll('.selection-card.selected[data-student-id]'))
                .map(card => card.dataset.studentId);
            const sitesToUse = Array.from(document.querySelectorAll('.selection-card1.selected[data-site]'))
                .map(card => card.dataset.site);

            console.log("DOM selected students:", studentsToSchedule);
            console.log("DOM selected sites:", sitesToUse);

            // Validate selections
            if (!studentsToSchedule || studentsToSchedule.length === 0) {
                alert('Please select at least one student');
                return;
            }

            if (!sitesToUse || sitesToUse.length === 0) {
                alert('Please select at least one hospital site');
                return;
            }

            const startDate = document.getElementById('start-date').value;
            const endDate = document.getElementById('end-date').value;

            if (!startDate || !endDate) {
                alert('Please select start and end dates');
                return;
            }

            // Show loading
            document.getElementById('loading').style.display = 'flex';
            document.getElementById('generate-btn').disabled = true;

            // Call the mock schedule generator with explicit parameters
            try {
                // Call with explicit parameters
                generateMockScheduleWithParams(studentsToSchedule, sitesToUse);

                // Hide loading
                document.getElementById('loading').style.display = 'none';
                document.getElementById('generate-btn').disabled = false;

                // Show success message
                document.getElementById('schedule-success').style.display = 'block';

                // Update the calendar view
                if (window.calendar) {
                    window.calendar.render();
                }
            } catch (error) {
                console.error("Error generating schedule:", error);
                document.getElementById('loading').style.display = 'none';
                document.getElementById('generate-btn').disabled = false;
                alert('An error occurred while generating the schedule: ' + error.message);
            }
        }

        function generateMockScheduleWithParams(students, sites) {
            console.log("Starting generateMockScheduleWithParams");
            console.log("Students:", students);
            console.log("Sites:", sites);

            if (!students || !Array.isArray(students) || students.length === 0) {
                throw new Error("No students provided for scheduling");
            }

            if (!sites || !Array.isArray(sites) || sites.length === 0) {
                throw new Error("No sites provided for scheduling");
            }

            // Get form values
            const startDateString = document.getElementById('start-date').value;
            const endDateString = document.getElementById('end-date').value;

            const startDateParts = startDateString.split('/');
            const endDateParts = endDateString.split('/');

            const startDate = new Date(Date.UTC(startDateParts[2], startDateParts[0] - 1, startDateParts[1]));
            const endDate = new Date(Date.UTC(endDateParts[2], endDateParts[0] - 1, endDateParts[1]));

            const includeWeekends = document.getElementById('include-weekends').checked;
            const holidayDates = document.getElementById('holiday-dates').value
                .split(',')
                .map(date => date.trim())
                .filter(date => date);

            // Ensure calendar is initialized
            if (!window.calendar) {
                console.error("Calendar not initialized");
                initializeCalendar();
            }

            // Clear existing events
            if (window.calendar) {
                window.calendar.removeAllEvents();
            }

            // Store colors assigned to each site for consistency
            let siteColors = {};
            scheduleEvents = []; // Reset schedule events array

            // Assign colors to sites - with extra validation
            sites.forEach((siteId, index) => {
                // Generate colors based on index
                const hue = (index * 137) % 360; // Golden ratio to distribute colors
                siteColors[siteId] = `hsl(${hue}, 70%, 60%)`;
            });

            // Determine which unique sites each student is allowed to visit
            const numSitesToVisit = parseInt(document.getElementById('sites-per-student').value, 10) || 3;
            console.log("Sites per student:", numSitesToVisit);

            const studentAllowedSites = {};
            students.forEach(student => {
                const shuffledSites = [...sites].sort(() => 0.5 - Math.random());
                studentAllowedSites[student] = shuffledSites.slice(0, Math.min(numSitesToVisit, sites.length));
            });

            // Generate schedule for each day
            let current = new Date(startDate);
            let eventCount = 0;

            while (current <= endDate) {
                const dayName = current.toLocaleDateString('en-US', {
                    weekday: 'long',
                    timeZone: 'UTC'
                });
                const currentDateString = current.toISOString().split('T')[0]; // YYYY-MM-DD format

                // Skip if it's a holiday OR (if includeWeekends is false AND it's a weekend)
                if (holidayDates.includes(currentDateString) ||
                    (!includeWeekends && (dayName === "Saturday" || dayName === "Sunday"))) {
                    current.setUTCDate(current.getUTCDate() + 1);
                    continue;
                }

                // Track daily assignments to enforce max students per site
                let dailySiteAssignments = {};

                // Shuffle students to randomize daily assignment order
                const shuffledStudents = [...students].sort(() => 0.5 - Math.random());

                shuffledStudents.forEach(studentId => {
                    // Get sites this student is allowed to visit
                    const allowedSites = studentAllowedSites[studentId] || [];

                    // Filter to sites that are open on this day of the week and have capacity
                    const availableSites = allowedSites.filter(siteId => {
                        // Find the site info from the HOSPITAL_SITES array
                        const siteInfo = HOSPITAL_SITES.find(site => site.id === siteId);
                        if (!siteInfo) return false;

                        const siteDays = siteInfo.days || [];
                        const siteCapacity = parseInt(siteInfo.capacity) || 3;

                        // Check if site is open on this day
                        if (!siteDays.includes(dayName)) return false;

                        // Check if site has capacity left
                        const currentAssignments = dailySiteAssignments[siteId] || 0;
                        return currentAssignments < siteCapacity;
                    });

                    if (availableSites.length > 0) {
                        // Assign student to a random available site
                        const assignedSite = availableSites[Math.floor(Math.random() * availableSites.length)];

                        // Update daily assignments count
                        dailySiteAssignments[assignedSite] = (dailySiteAssignments[assignedSite] || 0) + 1;

                        // Find the site info
                        const siteInfo = HOSPITAL_SITES.find(site => site.id === assignedSite);
                        const siteName = siteInfo ? siteInfo.name : assignedSite;
                        const siteTime = siteInfo ? siteInfo.time : "9:00 AM - 5:00 PM";
                        const siteCode = siteInfo ? siteInfo.hospitalSiteCode : "";

                        // Find the student name from the STUDENTS array
                        const studentInfo = STUDENTS.find(student => student.id === studentId);
                        const studentName = studentInfo ? studentInfo.name : studentId;

                        // Add event to calendar
                        try {
                            window.calendar.addEvent({
                                id: 'event-' + Date.now() + Math.random(), // Generate a unique ID
                                title: `${studentName} - ${siteName} (${siteTime})`,
                                start: currentDateString,
                                allDay: true,
                                backgroundColor: siteColors[assignedSite],
                                borderColor: siteColors[assignedSite],
                                extendedProps: {
                                    student: studentId,
                                    studentName: studentName,
                                    site: assignedSite,
                                    siteName: siteName
                                }
                            });
                            eventCount++;

                            // Store event for CSV export
                            scheduleEvents.push({
                                student: studentName,
                                site: siteName,
                                date: currentDateString,
                                time: siteTime
                            });
                        } catch (error) {
                            console.error("Error adding event:", error);
                        }
                    }
                });

                current.setUTCDate(current.getUTCDate() + 1); // Move to the next day
            }

            console.log("Schedule generation complete. Added", eventCount, "events");
            console.log("Total events in scheduleEvents array:", scheduleEvents.length);

            // Make sure the calendar is visible and refreshed
            document.getElementById('calendar-container').style.display = 'block';
            window.calendar.render();
        }

        function getRandomColor() {
            // A palette of darker, more vibrant colors for better contrast with white text
            const colors = [
                '#4A148C', // Deep Purple
                '#1A237E', // Indigo
                '#0D47A1', // Dark Blue
                '#004D40', // Dark Teal
                '#1B5E20', // Dark Green
                '#BF360C', // Deep Orange
                '#D84315', // Orange
                '#E64A19', // Dark Orange
                '#C62828', // Dark Red
                '#AD1457', // Dark Pink
                '#6A1B9A', // Purple
                '#424242', // Grey
                '#212121', // Dark Grey
                '#3E2723' // Brown
            ];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        function convertToMatrix(scheduleEvents) {
            const students = [...new Set(scheduleEvents.map(e => e.student))];
            const dates = [...new Set(scheduleEvents.map(e => e.date))];

            // Create header row: ["Student", "Mon 8/26", "Wed 8/28", ...]
            const dateHeaders = ["Student", ...dates];

            // Create a map for quick lookup: {student+date => time}
            const lookup = {};
            scheduleEvents.forEach(e => {
                lookup[`${e.student}-${e.date}`] = e.time;
            });

            // Build matrix
            const matrixData = students.map(student => {
                const row = [student];
                dates.forEach(date => {
                    row.push(lookup[`${student}-${date}`] || "");
                });
                return row;
            });

            return {
                matrixData,
                dateHeaders
            };
        }

        function exportSchedule() {
            if (!scheduleEvents || scheduleEvents.length === 0) {
                alert("Please generate a schedule first");
                return;
            }

            // Convert to Matrix
            const students = [...new Set(scheduleEvents.map(e => e.student))];
            const dates = [...new Set(scheduleEvents.map(e => e.date))];

            // Headers
            const headers = ["Student", ...dates];

            // Row Mapping
            const data = [headers];
            const lookup = {};
            scheduleEvents.forEach(e => {
                lookup[`${e.student}-${e.date}`] = e.time;
            });

            students.forEach(student => {
                const row = [student];
                dates.forEach(date => {
                    row.push(lookup[`${student}-${date}`] || "");
                });
                data.push(row);
            });

            // Create worksheet
            const ws = XLSX.utils.aoa_to_sheet(data);

            // Optional: Add simple cell styles
            const range = XLSX.utils.decode_range(ws['!ref']);
            for (let R = 1; R <= range.e.r; ++R) {
                for (let C = 1; C <= range.e.c; ++C) {
                    const cellAddress = XLSX.utils.encode_cell({
                        r: R,
                        c: C
                    });
                    if (!ws[cellAddress]) continue;
                    ws[cellAddress].s = {
                        fill: {
                            fgColor: {
                                rgb: "FFFFE0"
                            } // Light yellow
                        },
                        alignment: {
                            horizontal: "center"
                        }
                    };
                }
            }

            // Workbook
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, "Schedule");

            // Write file
            XLSX.writeFile(wb, "clinical-schedule.xlsx");
        }

        function exportScheduleAsJson() {
            console.log("Export JSON button clicked. Events:", scheduleEvents.length);

            if (!scheduleEvents || scheduleEvents.length === 0) {
                alert('Please generate a schedule first');
                return;
            }

            // Create structured JSON with students grouped by hospital sites
            const structuredSchedule = {};

            // First, organize by hospital site
            scheduleEvents.forEach(event => {
                const siteName = event.site;
                if (!structuredSchedule[siteName]) {
                    structuredSchedule[siteName] = {
                        students: {}
                    };
                }

                // Then by student within each site
                const studentName = event.student;
                if (!structuredSchedule[siteName].students[studentName]) {
                    structuredSchedule[siteName].students[studentName] = [];
                }

                // Add this visit to the student's schedule at this site
                structuredSchedule[siteName].students[studentName].push({
                    date: event.date,
                    time: event.time
                });
            });

            // Convert to JSON and download
            const jsonContent = JSON.stringify(structuredSchedule, null, 2);
            const blob = new Blob([jsonContent], {
                type: 'application/json'
            });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'clinical-schedule.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            console.log("JSON export complete");
        }

        function exportStudentSpecificJson() {
            console.log("Export Student-Specific JSON button clicked. Events:", scheduleEvents.length);

            if (!scheduleEvents || scheduleEvents.length === 0) {
                alert('Please generate a schedule first');
                return;
            }

            // Create structured JSON with hospital sites grouped by student
            const studentSchedules = {};

            // Process each event to organize by student
            scheduleEvents.forEach(event => {
                // Find the student info from the STUDENTS array
                const studentInfo = STUDENTS.find(student => student.name === event.student);
                if (!studentInfo) return;

                const studentId = studentInfo.id;
                const studentName = studentInfo.name;

                // Find the site info from the HOSPITAL_SITES array
                const siteInfo = HOSPITAL_SITES.find(site => site.name === event.site);
                if (!siteInfo) return;

                const siteId = siteInfo.id;
                const siteName = siteInfo.name;

                // Initialize student entry if it doesn't exist
                if (!studentSchedules[studentId]) {
                    studentSchedules[studentId] = {
                        studentId: studentId,
                        studentName: studentName,
                        hospitalSites: {}
                    };
                }

                // Initialize hospital site entry if it doesn't exist
                if (!studentSchedules[studentId].hospitalSites[siteId]) {
                    studentSchedules[studentId].hospitalSites[siteId] = {
                        hospitalSiteId: siteId,
                        hospitalSiteName: siteName,
                        dates: []
                    };
                }

                // Add this visit date to the student's schedule at this site
                studentSchedules[studentId].hospitalSites[siteId].dates.push({
                    date: event.date,
                    time: event.time
                });
            });

            // Convert to array format for easier consumption
            const studentSchedulesArray = Object.values(studentSchedules).map(student => {
                return {
                    studentId: student.studentId,
                    studentName: student.studentName,
                    hospitalSites: Object.values(student.hospitalSites)
                };
            });

            // Convert to JSON and download
            const jsonContent = JSON.stringify(studentSchedulesArray, null, 2);
            const blob = new Blob([jsonContent], {
                type: 'application/json'
            });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'student-clinical-schedule.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            console.log("Student-specific JSON export complete");
        }

        // Initialize first step
        showStep(1);

        // Add holiday date picker functionality
        $(document).ready(function() {
            const holidayInput = $('#holiday-dates');
            // Initialize the set from the input's value, in case it's pre-filled
            let holidays = new Set(holidayInput.val().split(', ').filter(Boolean));

            function updateInputAndHighlights() {
                // Update input
                const sortedHolidays = Array.from(holidays).sort((a, b) => new Date(a) - new Date(b));
                holidayInput.val(sortedHolidays.join(', '));

                // Update highlights
                const picker = $('#holiday-dates-group').data('DateTimePicker');
                if (picker) {
                    // A short timeout helps ensure the DOM is ready for class manipulation
                    setTimeout(function() {
                        $('#holiday-dates-group').find('.day').each(function() {
                            const day = $(this);
                            // The data-day attribute format can be inconsistent, so we parse it with moment.
                            const dayDate = moment(day.data('day'), 'M/D/YYYY').format('MM/DD/YYYY');
                            if (holidays.has(dayDate)) {
                                day.addClass('highlighted-holiday');
                            } else {
                                day.removeClass('highlighted-holiday');
                            }
                        });
                    }, 0);
                }
            }

            $('#holiday-dates-group').datetimepicker({
                format: 'MM/DD/YYYY',
                useCurrent: false,
                keepOpen: true,
            }).on('dp.change', function(e) {
                if (!e.date) return;
                const dateString = e.date.format('MM/DD/YYYY');

                // Toggle selection
                if (holidays.has(dateString)) {
                    holidays.delete(dateString);
                } else {
                    holidays.add(dateString);
                }

                // The picker automatically clears its date, which is what we want.
                // Now, just update our state.
                updateInputAndHighlights();

            }).on('dp.show', function() {
                // Refresh highlights when the picker is shown
                updateInputAndHighlights();
            }).on('dp.update', function() {
                // Refresh highlights when the month/year is changed
                updateInputAndHighlights();
            });

            // Make the input itself trigger the calendar
            holidayInput.on('click', function() {
                $('#holiday-dates-group').data('DateTimePicker').show();
            });
        });

        // Separate function to initialize the calendar
        // Initialize the calendar
        function initializeCalendar() {
            const calendarEl = document.getElementById('calendar');
            if (!calendarEl) {
                console.error("Calendar element not found!");
                return;
            }

            window.calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                editable: true,
                droppable: true,
                events: [],
                eventDidMount: function(info) {
                    // Add a close button to each event for removal
                    const closeBtn = document.createElement('span');
                    closeBtn.innerHTML = '×';
                    closeBtn.style.position = 'absolute';
                    closeBtn.style.top = '2px';
                    closeBtn.style.right = '4px';
                    closeBtn.style.color = 'white';
                    closeBtn.style.backgroundColor = 'red';
                    closeBtn.style.borderRadius = '50%';
                    closeBtn.style.padding = '0 6px';
                    closeBtn.style.fontSize = '12px';
                    closeBtn.style.cursor = 'pointer';
                    closeBtn.style.zIndex = '10';

                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        info.event.remove();
                    });

                    info.el.style.position = 'relative';
                    info.el.appendChild(closeBtn);
                },
                eventDragStart: function(info) {
    console.log('Drag started for event:', info.event.title);
    info.el.style.opacity = '0.8';
    info.el.style.zIndex = '9999';
    // Don't touch transform
    info.el.style.pointerEvents = 'none';

    info.el.classList.add('dragging-event');
},

                eventDragStop: function(info) {
                    console.log('Drag stopped for event:', info.event.title);
                    info.el.style.opacity = '';
                    info.el.style.zIndex = '';
                    info.el.style.transform = '';
                    info.el.style.pointerEvents = '';

                    // Remove the dragging class
                    info.el.classList.remove('dragging-event');
                },
                eventDrop: function(info) {
                    console.log('Event dropped:', info.event.title, 'on', info.event.start.toISOString());
                    // You can add additional logic here for handling the drop
                }
            });

            window.calendar.render();
        }

        // Call this function to initialize the calendar when needed
        initializeCalendar();

        function isValidDate(d) {
            return d instanceof Date && !isNaN(d);
        }

        // Function to fix calendar display issues
        function fixCalendarDisplay() {
            if (window.calendar) {
                console.log("Fixing calendar display");
                window.calendar.updateSize();

                // Force a month change and then back to fix rendering
                const currentDate = window.calendar.getDate();
                window.calendar.next(); // Go to next month
                setTimeout(() => {
                    window.calendar.prev(); // Go back to current month
                    console.log("Calendar display fixed");

                    // Fix calendar icons
                    fixCalendarIcons();
                }, 100);
            }
        }

        // Function to ensure calendar icons are displayed correctly
        function fixCalendarIcons() {
            // Make sure all FC icons are visible
            document.querySelectorAll('.fc-icon').forEach(icon => {
                icon.style.display = 'inline-block';
            });

            // Add missing icon classes if needed
            if (document.querySelector('.fc-prev-button .fc-icon') &&
                !document.querySelector('.fc-prev-button .fc-icon').classList.contains('fc-icon-chevron-left')) {
                document.querySelector('.fc-prev-button .fc-icon').classList.add('fc-icon-chevron-left');
            }

            if (document.querySelector('.fc-next-button .fc-icon') &&
                !document.querySelector('.fc-next-button .fc-icon').classList.contains('fc-icon-chevron-right')) {
                document.querySelector('.fc-next-button .fc-icon').classList.add('fc-icon-chevron-right');
            }
        }

        // Add CSS to ensure the calendar displays correctly
        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');

            window.calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                editable: true,
                droppable: true,
                events: [
                    // Sample events
                    {
                        title: 'Event 1',
                        start: new Date()
                    },
                    {
                        title: 'Event 2',
                        start: new Date(new Date().setDate(new Date().getDate() + 1))
                    }
                ],
                eventDragStart: function(info) {
                    info.el.style.opacity = '0.75';
                    info.el.style.zIndex = '1000';
                },
                eventDragStop: function(info) {
                    info.el.style.opacity = '';
                    info.el.style.zIndex = '';
                },
                eventDrop: function(info) {
                    console.log('Event dropped on ' + info.event.start.toISOString());
                },
                eventClick: function(info) {
                    // Handle event click - we'll show event details or edit options here
                    console.log('Event clicked:', info.event.title);

                    // You can add code here to show a modal with event details
                    // or other actions when an event is clicked
                },
                eventDidMount: function(info) {
                    // Create modern remove button
                    const closeBtn = document.createElement('button');
                    closeBtn.className = 'remove-event-btn';
                    closeBtn.innerHTML = '×';
                    closeBtn.title = 'Remove event';

                    closeBtn.addEventListener('click', function(e) {
                        e.stopPropagation();
                        e.preventDefault();

                        // Custom confirmation dialog with better styling
                        if (confirm('Are you sure you want to remove this scheduled event?')) {
                            info.event.remove();

                            // Optional: Add success feedback
                            console.log('Event removed successfully');
                        }
                    });

                    // Ensure the event container has relative positioning
                    info.el.style.position = 'relative';
                    info.el.style.overflow = 'visible';

                    // Add the remove button to the event
                    info.el.appendChild(closeBtn);

                    // Show/hide button on hover
                    info.el.addEventListener('mouseenter', function() {
                        closeBtn.style.display = 'flex';
                    });

                    info.el.addEventListener('mouseleave', function() {
                        closeBtn.style.display = 'none';
                    });
                }
            });

            window.calendar.render();
        });

        // Add this function to handle event removal
        function removeCalendarEvent(eventId) {
            if (confirm('Are you sure you want to remove this scheduled event?')) {
                // Find and remove the event from the calendar
                const event = window.calendar.getEventById(eventId);
                if (event) {
                    event.remove();
                    console.log(`Removed event: ${eventId}`);

                    // You may want to update your data structure that tracks events
                    scheduleEvents = scheduleEvents.filter(e => e.id !== eventId);

                    // Update the legend/summary if needed
                    updateSummary();
                }
            }
        }
        // Function to filter calendar events based on unified search
        function filterCalendarEvents() {
            const searchTerm = document.getElementById('unified-search').value.toLowerCase();
            const searchLoader = document.getElementById('search-loader');

            // Show loader
            searchLoader.style.display = 'block';

            // Use setTimeout to simulate a delay and ensure the loader is visible
            setTimeout(() => {
                const events = window.calendar.getEvents();
                events.forEach(event => {
                    const title = event.title.toLowerCase();
                    if (title.includes(searchTerm)) {
                        event.setProp('display', '');
                    } else {
                        event.setProp('display', 'none');
                    }
                });

                // Hide loader after filtering is done
                searchLoader.style.display = 'none';
            }, 300); // Adjust the delay as needed
        }

        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), delay);
            };
        }

        // Add event listener for the unified search input
        document.getElementById('unified-search').addEventListener('input', debounce(filterCalendarEvents, 300));



        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), delay);
            };
        };
        // Add event listener for the unified search input
        document.getElementById('unified-search').addEventListener('input', debounce(filterCalendarEvents, 300));

        // Testing and validation functions
        function validateSelectionState() {
            debugLog('=== Selection State Validation ===');
            debugLog('Selected Sites Array:', selectedSites);
            debugLog('Selected Students Array:', selectedStudents);

            // Check for duplicates in selectedSites
            const uniqueSites = [...new Set(selectedSites)];
            if (uniqueSites.length !== selectedSites.length) {
                console.error('DUPLICATE SITES DETECTED!', selectedSites);
                console.error('Duplicates found:', selectedSites.filter((item, index) => selectedSites.indexOf(item) !== index));
            } else {
                debugLog('✓ No duplicate sites found');
            }

            // Check for duplicates in selectedStudents
            const uniqueStudents = [...new Set(selectedStudents.map(s => s.id))];
            if (uniqueStudents.length !== selectedStudents.length) {
                console.error('DUPLICATE STUDENTS DETECTED!', selectedStudents);
                const studentIds = selectedStudents.map(s => s.id);
                console.error('Duplicate student IDs:', studentIds.filter((item, index) => studentIds.indexOf(item) !== index));
            } else {
                debugLog('✓ No duplicate students found');
            }

            // Validate DOM state matches arrays
            const domSelectedSites = $('.selection-card1.selected').length;
            const domSelectedStudents = $('.selection-card.selected').length;
            const totalSites = $('#sites-grid .selection-card1').length;
            const totalStudents = $('#students-grid .selection-card').length;

            debugLog(`DOM Sites Selected: ${domSelectedSites}/${totalSites}, Array Length: ${selectedSites.length}`);
            debugLog(`DOM Students Selected: ${domSelectedStudents}/${totalStudents}, Array Length: ${selectedStudents.length}`);

            if (domSelectedSites !== selectedSites.length) {
                console.error('SITE SELECTION MISMATCH between DOM and array!');
                console.error(`Expected: ${selectedSites.length}, Found in DOM: ${domSelectedSites}`);
            }
            if (domSelectedStudents !== selectedStudents.length) {
                console.error('STUDENT SELECTION MISMATCH between DOM and array!');
                console.error(`Expected: ${selectedStudents.length}, Found in DOM: ${domSelectedStudents}`);
            }

            // Check Select All checkbox states
            const selectAllSitesChecked = $('#select-all-sites-checkbox').prop('checked');
            const selectAllStudentsChecked = $('#select-all-checkbox').prop('checked');

            debugLog(`Select All Sites Checkbox: ${selectAllSitesChecked}, All Sites Selected: ${domSelectedSites === totalSites}`);
            debugLog(`Select All Students Checkbox: ${selectAllStudentsChecked}, All Students Selected: ${domSelectedStudents === totalStudents}`);
        }

        // Add validation to window for manual testing
        window.validateSelectionState = validateSelectionState;
        window.debugLog = debugLog;
    </script>
    <script>
        const grid = document.getElementById("students-grid");
        const wrapper = grid.parentElement;
        const cards = grid.querySelectorAll(".selection-card");

        if (cards.length > 20) {
            wrapper.style.maxHeight = "500px"; // or whatever you need
            wrapper.style.overflowY = "auto";
        } else {
            wrapper.style.maxHeight = "unset";
            wrapper.style.overflowY = "unset";
        }
    </script>

</body>

</html>